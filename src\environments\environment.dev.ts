import { Environment } from 'src/app/@core/base/environment';
export const environment: Environment = {
  mapTK: '7d930ac87cf5dc338644bec82cfb80b2',
  projectName: '巡检APP(开发环境)',
  production: true,
  openProxy: false,
  version: 'v1.1.8',
  // 加密配置 - 开发环境
  encryption: {
    enabled: false,        // 启用加密
    debugEnabled: false    // 启用调试日志
  },

  /*****************159 ********************/
  api: {
    mapUrl: 'https://**************:8102',
    ip: '**************',
    port: 8102,
    version: 't1'
  },

  /*****************157********************/
  // api: {
  //   mapUrl: 'http://**************:8096',
  //   ip: '**************',
  //   port: 8080,
  //   version: 't1'
  // },

  /*****************159 外网地址 https********************/
  // api: {
  // mapUrl: 'https://**************:8225',
  //   ip: '**************',
  //   port: 8225,
  //   version: 't1'
  // },
};
